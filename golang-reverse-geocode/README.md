# 坐标反查地名工具 (Go版本)

这是一个使用Go语言实现的坐标反查地名工具，支持通过经纬度坐标查询对应的地理位置信息。

## 功能特点

- 🌍 **多数据源支持**: 同时使用Google Maps和OpenStreetMap两个数据源
- 🇨🇳 **中文本地化**: 支持中文地址显示和组件解析
- 📊 **详细信息**: 提供完整的地址组件分析
- 💾 **结果保存**: 自动保存JSON格式的详细结果
- ⚡ **高性能**: Go语言实现，执行效率高
- 🛡️ **错误处理**: 完善的异常处理机制

## 使用方法

### 1. 运行程序

```bash
cd golang-reverse-geocode
go run main.go
```

### 2. 修改测试坐标

编辑 `main.go` 文件中的测试坐标：

```go
// 测试坐标
testLat := 25.102341196351926  // 纬度
testLng := 121.54851212861129  // 经度
```

### 3. 自定义API密钥

如果需要使用自己的Google Maps API密钥，请修改：

```go
// Google Maps API密钥
apiKey := "YOUR_API_KEY_HERE"
```

## 输出示例

```
============================================================
坐标反查地名工具 (Go版本)
============================================================
正在查询坐标: 25.102341, 121.548512
--------------------------------------------------
1. 使用Google Geocoding API查询...
   ✓ Google地址: 111台湾台北市士林區至善路二段221號

2. 使用OpenStreetMap Nominatim查询...
   ✓ OSM地址: 國立故宮博物院, 221, 至善路二段, 臨溪里, 士林區, 石角, 臺北市, 111, 臺灣

============================================================
详细结果
============================================================

🌍 Google Maps 结果:
完整地址: 111台湾台北市士林區至善路二段221號
位置类型: ROOFTOP
Place ID: ChIJs3Kg0jqsQjQRhdsK60RK2zw

地址组件:
市: 士林區
省/州: 台北市
国家: 台湾
邮编: 111
门牌号: 221
路: 至善路二段
区/县: 臨溪里

🗺️ OpenStreetMap 结果:
完整地址: 國立故宮博物院, 221, 至善路二段, 臨溪里, 士林區, 石角, 臺北市, 111, 臺灣
OSM类型: way
OSM ID: 827791918

地址组件:
国家: 臺灣

💾 详细结果已保存到: reverse_geocode_result_go_1755224346.json
```

## 项目结构

```
golang-reverse-geocode/
├── main.go          # 主程序文件
├── go.mod           # Go模块文件
└── README.md        # 说明文档
```

## 依赖说明

本项目仅使用Go标准库，无需额外依赖：

- `encoding/json` - JSON处理
- `net/http` - HTTP客户端
- `net/url` - URL处理
- `fmt` - 格式化输出
- `os` - 文件操作
- `time` - 时间处理

## API说明

### Google Geocoding API
- 提供高精度的地理编码服务
- 支持中文本地化
- 需要API密钥

### OpenStreetMap Nominatim API
- 免费的地理编码服务
- 开源数据
- 有请求频率限制

## 注意事项

1. **API限制**: Google Maps API有使用配额限制
2. **请求频率**: Nominatim API要求限制请求频率（1秒间隔）
3. **网络连接**: 需要稳定的网络连接访问API服务
4. **坐标格式**: 使用WGS84坐标系（GPS坐标）

## 许可证

MIT License
