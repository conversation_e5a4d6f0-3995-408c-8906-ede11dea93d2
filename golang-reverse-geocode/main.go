package main

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"
)

// GoogleGeocodeResponse Google API响应结构
type GoogleGeocodeResponse struct {
	Status  string `json:"status"`
	Results []struct {
		FormattedAddress string `json:"formatted_address"`
		PlaceID          string `json:"place_id"`
		AddressComponents []struct {
			LongName  string   `json:"long_name"`
			ShortName string   `json:"short_name"`
			Types     []string `json:"types"`
		} `json:"address_components"`
		Geometry struct {
			LocationType string `json:"location_type"`
		} `json:"geometry"`
	} `json:"results"`
}

// NominatimResponse OpenStreetMap Nominatim API响应结构
type NominatimResponse struct {
	DisplayName string            `json:"display_name"`
	PlaceID     interface{}       `json:"place_id"` // 可能是字符串或数字
	OSMType     string            `json:"osm_type"`
	OSMID       interface{}       `json:"osm_id"`   // 可能是字符串或数字
	Address     map[string]string `json:"address"`
}

// LocationResult 位置查询结果
type LocationResult struct {
	Status           string            `json:"status"`
	FormattedAddress string            `json:"formatted_address"`
	Components       map[string]string `json:"components"`
	PlaceID          string            `json:"place_id"`
	LocationType     string            `json:"location_type,omitempty"`
	OSMType          string            `json:"osm_type,omitempty"`
	OSMID            string            `json:"osm_id,omitempty"`
	Message          string            `json:"message,omitempty"`
}

// ReverseGeocoder 反向地理编码器
type ReverseGeocoder struct {
	GoogleAPIKey string
	HTTPClient   *http.Client
}

// NewReverseGeocoder 创建新的反向地理编码器
func NewReverseGeocoder(apiKey string) *ReverseGeocoder {
	return &ReverseGeocoder{
		GoogleAPIKey: apiKey,
		HTTPClient: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// GoogleReverseGeocode 使用Google Geocoding API进行反向地理编码
func (rg *ReverseGeocoder) GoogleReverseGeocode(lat, lng float64, language string) LocationResult {
	baseURL := "https://maps.googleapis.com/maps/api/geocode/json"
	
	params := url.Values{}
	params.Add("latlng", fmt.Sprintf("%.6f,%.6f", lat, lng))
	params.Add("key", rg.GoogleAPIKey)
	params.Add("language", language)
	if language == "zh-CN" {
		params.Add("region", "CN")
	}
	
	fullURL := baseURL + "?" + params.Encode()
	
	resp, err := rg.HTTPClient.Get(fullURL)
	if err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("读取响应失败: %v", err),
		}
	}
	
	var googleResp GoogleGeocodeResponse
	if err := json.Unmarshal(body, &googleResp); err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("解析JSON失败: %v", err),
		}
	}
	
	if googleResp.Status != "OK" || len(googleResp.Results) == 0 {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("Google API错误: %s", googleResp.Status),
		}
	}
	
	result := googleResp.Results[0]
	components := make(map[string]string)
	
	// 解析地址组件
	for _, component := range result.AddressComponents {
		for _, componentType := range component.Types {
			components[componentType] = component.LongName
		}
	}
	
	return LocationResult{
		Status:           "success",
		FormattedAddress: result.FormattedAddress,
		Components:       components,
		PlaceID:          result.PlaceID,
		LocationType:     result.Geometry.LocationType,
	}
}

// NominatimReverseGeocode 使用OpenStreetMap Nominatim API进行反向地理编码
func (rg *ReverseGeocoder) NominatimReverseGeocode(lat, lng float64, language string) LocationResult {
	baseURL := "https://nominatim.openstreetmap.org/reverse"
	
	params := url.Values{}
	params.Add("lat", strconv.FormatFloat(lat, 'f', 6, 64))
	params.Add("lon", strconv.FormatFloat(lng, 'f', 6, 64))
	params.Add("format", "json")
	params.Add("addressdetails", "1")
	params.Add("accept-language", language)
	params.Add("zoom", "18")
	
	fullURL := baseURL + "?" + params.Encode()
	
	// Nominatim要求限制请求频率
	time.Sleep(1 * time.Second)
	
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("创建请求失败: %v", err),
		}
	}
	
	// 设置User-Agent，Nominatim要求
	req.Header.Set("User-Agent", "ReverseGeocoder/1.0")
	
	resp, err := rg.HTTPClient.Do(req)
	if err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("请求失败: %v", err),
		}
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("读取响应失败: %v", err),
		}
	}
	
	var nominatimResp NominatimResponse
	if err := json.Unmarshal(body, &nominatimResp); err != nil {
		return LocationResult{
			Status:  "error",
			Message: fmt.Sprintf("解析JSON失败: %v", err),
		}
	}
	
	if nominatimResp.DisplayName == "" {
		return LocationResult{
			Status:  "error",
			Message: "未找到地址信息",
		}
	}
	
	// 处理PlaceID和OSMID的类型转换
	placeID := ""
	osmID := ""
	
	if nominatimResp.PlaceID != nil {
		switch v := nominatimResp.PlaceID.(type) {
		case string:
			placeID = v
		case float64:
			placeID = strconv.FormatFloat(v, 'f', 0, 64)
		}
	}
	
	if nominatimResp.OSMID != nil {
		switch v := nominatimResp.OSMID.(type) {
		case string:
			osmID = v
		case float64:
			osmID = strconv.FormatFloat(v, 'f', 0, 64)
		}
	}
	
	return LocationResult{
		Status:           "success",
		FormattedAddress: nominatimResp.DisplayName,
		Components:       nominatimResp.Address,
		PlaceID:          placeID,
		OSMType:          nominatimResp.OSMType,
		OSMID:            osmID,
	}
}

// FormatAddressComponents 格式化地址组件
func FormatAddressComponents(components map[string]string, isGoogle bool) string {
	var googleMapping = map[string]string{
		"country":                      "国家",
		"administrative_area_level_1":  "省/州",
		"administrative_area_level_2":  "市",
		"administrative_area_level_3":  "区/县",
		"locality":                     "城市",
		"sublocality":                  "街道",
		"route":                        "路",
		"street_number":                "门牌号",
		"postal_code":                  "邮编",
	}
	
	var osmMapping = map[string]string{
		"country":      "国家",
		"state":        "省/州",
		"city":         "市",
		"county":       "区/县",
		"town":         "镇",
		"village":      "村",
		"suburb":       "街道",
		"road":         "路",
		"house_number": "门牌号",
		"postcode":     "邮编",
	}
	
	var parts []string
	var mapping map[string]string
	
	if isGoogle {
		mapping = googleMapping
	} else {
		mapping = osmMapping
	}
	
	for key, value := range components {
		if chineseName, exists := mapping[key]; exists && value != "" {
			parts = append(parts, fmt.Sprintf("%s: %s", chineseName, value))
		}
	}
	
	if len(parts) == 0 {
		return "无详细组件信息"
	}
	
	return strings.Join(parts, "\n")
}

// ReverseGeocodeMultipleSources 使用多个数据源进行反向地理编码
func (rg *ReverseGeocoder) ReverseGeocodeMultipleSources(lat, lng float64) map[string]LocationResult {
	results := make(map[string]LocationResult)
	
	fmt.Printf("正在查询坐标: %.6f, %.6f\n", lat, lng)
	fmt.Println(strings.Repeat("-", 50))
	
	// Google Geocoding API
	fmt.Println("1. 使用Google Geocoding API查询...")
	googleResult := rg.GoogleReverseGeocode(lat, lng, "zh-CN")
	results["google"] = googleResult
	
	if googleResult.Status == "success" {
		fmt.Printf("   ✓ Google地址: %s\n", googleResult.FormattedAddress)
	} else {
		fmt.Printf("   ✗ Google查询失败: %s\n", googleResult.Message)
	}
	
	// OpenStreetMap Nominatim
	fmt.Println("\n2. 使用OpenStreetMap Nominatim查询...")
	osmResult := rg.NominatimReverseGeocode(lat, lng, "zh")
	results["openstreetmap"] = osmResult
	
	if osmResult.Status == "success" {
		fmt.Printf("   ✓ OSM地址: %s\n", osmResult.FormattedAddress)
	} else {
		fmt.Printf("   ✗ OSM查询失败: %s\n", osmResult.Message)
	}
	
	return results
}

func main() {
	// Google Maps API密钥
	apiKey := "AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI"
	
	// 测试坐标
	testLat := 25.102341196351926
	testLng := 121.54851212861129
	
	fmt.Println(strings.Repeat("=", 60))
	fmt.Println("坐标反查地名工具 (Go版本)")
	fmt.Println(strings.Repeat("=", 60))
	
	// 创建反向地理编码器
	geocoder := NewReverseGeocoder(apiKey)
	
	// 执行反向地理编码
	results := geocoder.ReverseGeocodeMultipleSources(testLat, testLng)
	
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("详细结果")
	fmt.Println(strings.Repeat("=", 60))
	
	// 显示Google结果
	if googleResult, exists := results["google"]; exists && googleResult.Status == "success" {
		fmt.Println("\n🌍 Google Maps 结果:")
		fmt.Printf("完整地址: %s\n", googleResult.FormattedAddress)
		fmt.Printf("位置类型: %s\n", googleResult.LocationType)
		fmt.Printf("Place ID: %s\n", googleResult.PlaceID)
		fmt.Println("\n地址组件:")
		fmt.Println(FormatAddressComponents(googleResult.Components, true))
	}
	
	// 显示OSM结果
	if osmResult, exists := results["openstreetmap"]; exists && osmResult.Status == "success" {
		fmt.Println("\n🗺️ OpenStreetMap 结果:")
		fmt.Printf("完整地址: %s\n", osmResult.FormattedAddress)
		fmt.Printf("OSM类型: %s\n", osmResult.OSMType)
		fmt.Printf("OSM ID: %s\n", osmResult.OSMID)
		fmt.Println("\n地址组件:")
		fmt.Println(FormatAddressComponents(osmResult.Components, false))
	}
	
	// 保存结果到JSON文件
	outputFile := fmt.Sprintf("reverse_geocode_result_go_%d.json", time.Now().Unix())
	jsonData, err := json.MarshalIndent(results, "", "  ")
	if err != nil {
		fmt.Printf("JSON序列化失败: %v\n", err)
		return
	}
	
	if err := os.WriteFile(outputFile, jsonData, 0644); err != nil {
		fmt.Printf("保存文件失败: %v\n", err)
		return
	}
	
	fmt.Printf("\n💾 详细结果已保存到: %s\n", outputFile)
}
